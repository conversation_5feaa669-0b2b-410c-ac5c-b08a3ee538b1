import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  Dimensions,
  TextInput,
  TouchableOpacity,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { MaterialIcons } from '@expo/vector-icons';
import { SessionSummary, TaskType, TASK_TYPES, TimerMode } from '@/types/app';
import { useTheme } from '@/contexts/ThemeContext';
import { Button, Input, Modal } from '@/components/ui';
import { useSessionStore } from '@/stores/sessionStore';

interface SessionSummaryModalProps {
  visible: boolean;
  onClose: () => void;
  onSubmit: (summary: SessionSummary) => void;
  sessionId?: string;
  sessionDuration: number;
  subject?: string;
  subjectColor?: string;
  mode?: TimerMode;
  phase?: 'work' | 'shortBreak' | 'longBreak';
  initialTaskName?: string;
  initialTaskType?: TaskType;
  userId: string;
}

const { width } = Dimensions.get('window');

export function SessionSummaryModal({
  visible,
  onClose,
  onSubmit,
  sessionId,
  sessionDuration,
  subject,
  subjectColor,
  mode = 'stopwatch',
  phase,
  initialTaskName = '',
  initialTaskType = 'General Study',
  userId,
}: SessionSummaryModalProps) {
  const { theme } = useTheme();
  const { completeSession, isLoading } = useSessionStore();

  const [taskName, setTaskName] = useState(initialTaskName);
  const [taskType, setTaskType] = useState<TaskType>(initialTaskType);
  const [productivityRating, setProductivityRating] = useState<number>(0);
  const [notes, setNotes] = useState('');
  const [feedback, setFeedback] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const formatDuration = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`;
    }
    return `${secs}s`;
  };

  const getSessionTypeLabel = (): string => {
    if (mode === 'pomodoro') {
      switch (phase) {
        case 'work':
          return 'Pomodoro Work Session';
        case 'shortBreak':
          return 'Short Break';
        case 'longBreak':
          return 'Long Break';
        default:
          return 'Pomodoro Session';
      }
    }
    return 'Study Session';
  };

  const handleSubmit = async () => {
    if (isSubmitting) return;

    setIsSubmitting(true);

    try {
      const summary: SessionSummary = {
        taskName: taskName.trim() || '',
        taskType,
        productivityRating: productivityRating > 0 ? productivityRating : undefined,
        notes: notes.trim() || undefined,
        feedback: feedback.trim() || undefined,
      };

      // If sessionId is provided, complete the session in the store
      if (sessionId) {
        await completeSession(sessionId, summary);
      }

      onSubmit(summary);
      resetForm();
      onClose();
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to save session');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSkip = () => {
    onSubmit({});
    resetForm();
    onClose();
  };

  const resetForm = () => {
    setTaskName('');
    setTaskType('General Study');
    setProductivityRating(0);
    setNotes('');
    setFeedback('');
  };

  const renderStarRating = () => {
    const ratingLabels = [
      'Not productive',
      'Somewhat productive',
      'Moderately productive',
      'Very productive',
      'Extremely productive'
    ];

    return (
      <View style={[styles.ratingContainer, { backgroundColor: theme.colors.background.card }]}>
        <View style={styles.starContainer}>
          {[1, 2, 3, 4, 5].map((star) => (
            <Animated.View
              key={star}
              style={{
                transform: [{
                  scale: productivityRating === star ? 1.2 : 1
                }]
              }}
            >
              <Button
                title=""
                onPress={() => setProductivityRating(star)}
                variant="ghost"
                size="sm"
                style={[
                  styles.starButton,
                  {
                    backgroundColor: star <= productivityRating
                      ? theme.colors.accent.primary + '20'
                      : 'transparent'
                  }
                ] as any}
                icon={
                  <MaterialIcons
                    name={star <= productivityRating ? 'star' : 'star-border'}
                    size={28}
                    color={star <= productivityRating ? theme.colors.accent.primary : theme.colors.text.secondary}
                  />
                }
              />
            </Animated.View>
          ))}
        </View>
        {productivityRating > 0 && (
          <Animated.View style={{ opacity: fadeAnim }}>
            <Text style={[styles.ratingLabel, { color: theme.colors.text.secondary }]}>
              {ratingLabels[productivityRating - 1]}
            </Text>
          </Animated.View>
        )}
      </View>
    );
  };

  const renderTaskTypeSelector = () => {
    return (
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.taskTypeScroll}
        contentContainerStyle={styles.taskTypeContent}
      >
        {TASK_TYPES.map((type) => (
          <Button
            key={type}
            title={type}
            onPress={() => setTaskType(type)}
            variant={taskType === type ? "primary" : "outline"}
            size="sm"
            style={[
              styles.taskTypeChip,
              {
                backgroundColor: taskType === type
                  ? theme.colors.accent.primary
                  : theme.colors.background.card,
                borderColor: taskType === type
                  ? theme.colors.accent.primary
                  : theme.colors.ui.border,
              }
            ] as any}
            textStyle={{
              color: taskType === type
                ? theme.colors.text.inverse
                : theme.colors.text.primary,
              fontSize: 14,
              fontWeight: '500',
            } as any}
          />
        ))}
      </ScrollView>
    );
  };

  return (
    <Modal
      visible={visible}
      onClose={onClose}
      size="lg"
      showCloseButton={true}
      title={`${getSessionTypeLabel()} Complete!`}
      scrollable={false}
    >
      <View style={[styles.container]}>
        {/* Celebration Header */}
        <LinearGradient
          colors={[theme.colors.accent.primary + '15', theme.colors.background.primary]}
          style={styles.headerGradient}
        >
          <View style={styles.celebrationHeader}>
            <LinearGradient
              colors={[theme.colors.accent.primary, theme.colors.accent.secondary || theme.colors.accent.primary]}
              style={styles.celebrationIcon}
            >
              <MaterialIcons name="celebration" size={28} color="#FFFFFF" />
            </LinearGradient>
            <Text style={[styles.subtitle, { color: theme.colors.text.secondary }]}>
              Great work! Let's capture some details about your session.
            </Text>
          </View>
        </LinearGradient>

        <ScrollView
          style={styles.content}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
        >
          {/* Session Summary Card */}
          <LinearGradient
            colors={[theme.colors.background.card, theme.colors.background.card + 'F0']}
            style={[
              styles.summaryCard,
              {
                borderColor: theme.colors.accent.primary + '20',
              }
            ]}
          >
            <View style={styles.summaryHeader}>
              <LinearGradient
                colors={[theme.colors.accent.primary, theme.colors.accent.secondary || theme.colors.accent.primary]}
                style={styles.summaryIconContainer}
              >
                <MaterialIcons
                  name="timer"
                  size={24}
                  color="#FFFFFF"
                />
              </LinearGradient>
              <Text style={[styles.summaryTitle, { color: theme.colors.text.primary }]}>
                Session Summary
              </Text>
            </View>

            <View style={styles.summaryContent}>
              <View style={styles.summaryRow}>
                <Text style={[styles.summaryLabel, { color: theme.colors.text.secondary }]}>
                  Duration:
                </Text>
                <Text style={[styles.summaryValue, { color: theme.colors.text.primary }]}>
                  {formatDuration(sessionDuration)}
                </Text>
              </View>

              {subject && (
                <View style={styles.summaryRow}>
                  <Text style={[styles.summaryLabel, { color: theme.colors.text.secondary }]}>
                    Subject:
                  </Text>
                  <View style={styles.subjectContainer}>
                    {subjectColor && (
                      <View
                        style={[
                          styles.subjectColorDot,
                          { backgroundColor: subjectColor }
                        ]}
                      />
                    )}
                    <Text style={[styles.summaryValue, { color: theme.colors.text.primary }]}>
                      {subject}
                    </Text>
                  </View>
                </View>
              )}

              <View style={styles.summaryRow}>
                <Text style={[styles.summaryLabel, { color: theme.colors.text.secondary }]}>
                  Type:
                </Text>
                <Text style={[styles.summaryValue, { color: theme.colors.text.primary }]}>
                  {getSessionTypeLabel()}
                </Text>
              </View>
            </View>
          </LinearGradient>

          {/* Task Details */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text.primary }]}>
              What did you work on?
            </Text>
            <Text style={[styles.sectionSubtitle, { color: theme.colors.text.secondary }]}>
              Describe what you accomplished during this session
            </Text>
            <TextInput
              style={[
                styles.textInput,
                {
                  backgroundColor: theme.colors.background.card,
                  borderColor: theme.colors.ui.border,
                  color: theme.colors.text.primary,
                }
              ]}
              placeholder="e.g., Chapter 5 exercises, Essay writing..."
              placeholderTextColor={theme.colors.text.secondary}
              value={taskName}
              onChangeText={setTaskName}
              multiline
              maxLength={100}
            />
          </View>

          {/* Task Type */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text.primary }]}>
              Task Type
            </Text>
            <Text style={[styles.sectionSubtitle, { color: theme.colors.text.secondary }]}>
              Select the type of work you did
            </Text>
            {renderTaskTypeSelector()}
          </View>

          {/* Productivity Rating */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text.primary }]}>
              How productive did you feel?
            </Text>
            <Text style={[styles.sectionSubtitle, { color: theme.colors.text.secondary }]}>
              Rate your focus and productivity level
            </Text>
            {renderStarRating()}
          </View>

          {/* Notes */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text.primary }]}>
              Session Notes
              <Text style={[styles.optionalText, { color: theme.colors.text.secondary }]}>
                {' '}(Optional)
              </Text>
            </Text>
            <Text style={[styles.sectionSubtitle, { color: theme.colors.text.secondary }]}>
              Add any thoughts or observations about this session
            </Text>
            <TextInput
              style={[
                styles.textInput,
                styles.notesInput,
                {
                  backgroundColor: theme.colors.background.card,
                  borderColor: theme.colors.ui.border,
                  color: theme.colors.text.primary,
                }
              ]}
              placeholder="Any notes about this session..."
              placeholderTextColor={theme.colors.text.secondary}
              value={notes}
              onChangeText={setNotes}
              multiline
              maxLength={200}
            />
          </View>

          {/* Feedback */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text.primary }]}>
              Session Feedback
              <Text style={[styles.optionalText, { color: theme.colors.text.secondary }]}>
                {' '}(Optional)
              </Text>
            </Text>
            <Text style={[styles.sectionSubtitle, { color: theme.colors.text.secondary }]}>
              Share any challenges, insights, or improvements for next time
            </Text>
            <TextInput
              style={[
                styles.textInput,
                styles.notesInput,
                {
                  backgroundColor: theme.colors.background.card,
                  borderColor: theme.colors.ui.border,
                  color: theme.colors.text.primary,
                }
              ]}
              placeholder="How did this session go? Any challenges or insights?"
              placeholderTextColor={theme.colors.text.secondary}
              value={feedback}
              onChangeText={setFeedback}
              multiline
              maxLength={200}
            />
          </View>
        </ScrollView>

        {/* Action Buttons */}
        <LinearGradient
          colors={[theme.colors.background.primary + '00', theme.colors.background.primary]}
          style={[
            styles.actions,
            {
              borderTopColor: theme.colors.ui.border,
            }
          ]}
        >
          <Button
            title="Skip"
            onPress={handleSkip}
            variant="outline"
            size="lg"
            style={[
              styles.actionButton,
              {
                borderColor: theme.colors.ui.border,
                backgroundColor: theme.colors.background.secondary,
              }
            ]}
            textStyle={{ color: theme.colors.text.secondary } as any}
            disabled={isSubmitting}
          />
          <TouchableOpacity
            style={[
              styles.actionButton,
              styles.primaryButton,
              { opacity: isSubmitting ? 0.7 : 1 }
            ]}
            onPress={handleSubmit}
            disabled={isSubmitting}
          >
            <LinearGradient
              colors={[theme.colors.accent.primary, theme.colors.accent.secondary || theme.colors.accent.primary]}
              style={styles.primaryButtonGradient}
            >
              <Text style={[styles.primaryButtonText, { color: theme.colors.text.inverse }]}>
                {isSubmitting ? "Saving..." : "Save Session"}
              </Text>
              {!isSubmitting && (
                <MaterialIcons
                  name="check"
                  size={20}
                  color={theme.colors.text.inverse}
                  style={styles.buttonIcon}
                />
              )}
            </LinearGradient>
          </TouchableOpacity>
        </LinearGradient>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerGradient: {
    paddingBottom: 16,
    marginHorizontal: -24,
    marginTop: -24,
    paddingHorizontal: 24,
    paddingTop: 24,
  },
  celebrationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingBottom: 8,
  },
  celebrationIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 6,
  },
  subtitle: {
    fontSize: 16,
    lineHeight: 22,
    opacity: 0.8,
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
  },
  scrollContent: {
    paddingBottom: 24,
  },
  summaryCard: {
    borderRadius: 20,
    padding: 24,
    marginBottom: 28,
    borderWidth: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 6,
  },
  summaryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  summaryIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 4,
  },
  summaryTitle: {
    fontSize: 20,
    fontWeight: '700',
  },
  summaryContent: {
    gap: 12,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  summaryLabel: {
    fontSize: 16,
    fontWeight: '500',
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: '600',
  },
  subjectContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  subjectColorDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '700',
    marginBottom: 8,
  },
  sectionSubtitle: {
    fontSize: 15,
    marginBottom: 20,
    lineHeight: 22,
    opacity: 0.7,
  },
  optionalText: {
    fontSize: 14,
    fontWeight: '500',
    opacity: 0.6,
  },
  textInput: {
    borderRadius: 16,
    padding: 18,
    fontSize: 16,
    borderWidth: 1.5,
    minHeight: 56,
    fontWeight: '500',
  },
  notesInput: {
    minHeight: 120,
    textAlignVertical: 'top',
    lineHeight: 24,
  },
  taskTypeScroll: {
    marginBottom: 12,
  },
  taskTypeContent: {
    paddingRight: 24,
  },
  taskTypeChip: {
    borderRadius: 28,
    marginRight: 12,
    borderWidth: 1.5,
    minWidth: 90,
    paddingVertical: 12,
    paddingHorizontal: 20,
  },
  ratingContainer: {
    borderRadius: 20,
    padding: 24,
    marginVertical: 12,
  },
  starContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 20,
    gap: 12,
  },
  starButton: {
    borderRadius: 24,
    padding: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  ratingLabel: {
    textAlign: 'center',
    fontSize: 17,
    fontWeight: '600',
    fontStyle: 'italic',
  },
  actions: {
    flexDirection: 'row',
    paddingHorizontal: 24,
    paddingVertical: 24,
    borderTopWidth: 1,
    gap: 16,
  },
  actionButton: {
    flex: 1,
    borderRadius: 16,
    minHeight: 56,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  primaryButton: {
    flex: 2,
    overflow: 'hidden',
  },
  primaryButtonGradient: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 18,
    paddingHorizontal: 24,
    borderRadius: 16,
  },
  primaryButtonText: {
    fontSize: 16,
    fontWeight: '700',
    marginRight: 8,
  },
  buttonIcon: {
    marginLeft: 4,
  },
});
